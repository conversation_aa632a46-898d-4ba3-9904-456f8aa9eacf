// pages/index/index.js
const app = getApp();

Page({
  data: {
    // 轮播图数据
    banners: [
      {
        id: 1,
        image: '/images/banner/banner1.jpg',
        title: 'UW品牌新篇章',
        subtitle: '创新科技，品质生活',
        link: '/pages/products/products',
        type: 'page' // page: 页面跳转, url: 外部链接
      },
      {
        id: 2,
        image: '/images/banner/banner2.jpg',
        title: '智能产品系列',
        subtitle: '科技改变生活方式',
        link: '/pages/products/products?category=electronics',
        type: 'page'
      },
      {
        id: 3,
        image: '/images/banner/banner3.jpg',
        title: '品质保证',
        subtitle: '每一个细节都精益求精',
        link: '/pages/feedback/feedback',
        type: 'page'
      }
    ],

    // 当前轮播图索引
    currentBannerIndex: 0,

    // 页面滚动状态
    scrollTop: 0,

    // 品牌内容图片（暂时使用banner1.jpg作为占位符）
    brandIntroImage: '/images/banner/banner1.jpg', // 品牌介绍图片，后期由管理员上传
    brandHistoryImage: '/images/banner/banner1.jpg', // 品牌历程图片，后期由管理员上传


  },

  onLoad: function () {
    console.log('[Index] 首页加载');
    console.log('[Index] 轮播图数据:', this.data.banners);
    console.log('[Index] 轮播图数量:', this.data.banners.length);
    this.initPage();
  },

  onShow: function () {
    console.log('[Index] 首页显示');
    this.startAutoPlay();
  },

  onHide: function () {
    console.log('[Index] 首页隐藏');
    this.stopAutoPlay();
  },

  onUnload: function () {
    console.log('[Index] 首页卸载');
    this.stopAutoPlay();
  },

  /**
   * 初始化页面
   */
  initPage: function () {
    // 启动轮播图自动播放
    this.startAutoPlay();
  },

  /**
   * 启动轮播图自动播放
   */
  startAutoPlay: function () {
    this.stopAutoPlay(); // 先停止之前的定时器
    this.autoPlayTimer = setInterval(() => {
      const currentIndex = this.data.currentBannerIndex;
      const nextIndex = (currentIndex + 1) % this.data.banners.length;
      this.setData({
        currentBannerIndex: nextIndex
      });
    }, 4000); // 4秒切换一次
  },

  /**
   * 停止轮播图自动播放
   */
  stopAutoPlay: function () {
    if (this.autoPlayTimer) {
      clearInterval(this.autoPlayTimer);
      this.autoPlayTimer = null;
    }
  },

  /**
   * 轮播图切换
   */
  onBannerChange: function (e) {
    const current = e.detail.current;
    this.setData({
      currentBannerIndex: current
    });
  },

  /**
   * 轮播图点击
   */
  onBannerTap: function (e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];

    if (banner && banner.link) {
      if (banner.type === 'page') {
        wx.navigateTo({
          url: banner.link,
          fail: () => {
            wx.switchTab({
              url: banner.link
            });
          }
        });
      } else if (banner.type === 'url') {
        // 处理外部链接
        wx.showToast({
          title: '跳转外部链接',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 页面滚动监听
   */
  onPageScroll: function (e) {
    const scrollTop = e.scrollTop;
    this.setData({
      scrollTop
    });
  },





  /**
   * 分享小程序
   */
  onShareAppMessage: function () {
    return {
      title: 'UW品牌中心 - 创新科技，品质生活',
      path: '/pages/index/index',
      imageUrl: '/images/ai_example1.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: 'UW品牌中心 - 创新科技，品质生活',
      imageUrl: '/images/ai_example1.png'
    };
  }
});