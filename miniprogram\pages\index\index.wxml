<!--pages/index/index.wxml-->
<view class="container">
  <!-- 测试显示 -->
  <view style="padding: 20rpx; background: green; color: white; position: fixed; top: 0; left: 0; z-index: 999;">
    轮播图数量: {{banners.length}}
  </view>
  <!-- 测试轮播图数据 -->
  <view style="padding: 20rpx; margin-top: 60rpx;">
    <text>轮播图测试:</text>
    <view wx:for="{{banners}}" wx:key="id" style="margin: 10rpx 0; padding: 10rpx; background: #f0f0f0;">
      <text>{{index + 1}}. {{item.title}}</text>
      <image src="{{item.image}}" style="width: 100rpx; height: 60rpx; margin-left: 20rpx;" mode="aspectFill" />
    </view>
  </view>

  <!-- 简化轮播图区域 -->
  <view class="banner-section" style="background: yellow; margin: 20rpx;">
    <text>轮播图区域</text>
    <swiper class="banner-swiper" style="height: 200rpx; background: pink;">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <view style="background: blue; color: white; text-align: center; line-height: 200rpx;">
          图片 {{index + 1}}
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 品牌内容区域 -->
  <view class="content-section">
    <!-- 品牌介绍图片 -->
    <view class="brand-intro-section">
      <image
        class="brand-intro-image"
        src="{{brandIntroImage}}"
        mode="aspectFill"
        lazy-load="true"
      />
      <view class="image-placeholder" wx:if="{{!brandIntroImage}}">
        <view class="placeholder-text">品牌介绍图片</view>
        <view class="placeholder-desc">管理员可上传品牌介绍图片</view>
      </view>
    </view>

    <!-- 品牌历程图片 -->
    <view class="brand-history-section">
      <image
        class="brand-history-image"
        src="{{brandHistoryImage}}"
        mode="aspectFill"
        lazy-load="true"
      />
      <view class="image-placeholder" wx:if="{{!brandHistoryImage}}">
        <view class="placeholder-text">品牌历程图片</view>
        <view class="placeholder-desc">管理员可上传品牌历程图片</view>
      </view>
    </view>
  </view>
</view>