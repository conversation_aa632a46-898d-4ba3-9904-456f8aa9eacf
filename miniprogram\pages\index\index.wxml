<!--pages/index/index.wxml-->
<view class="container">
  <!-- 测试显示 -->
  <view style="padding: 20rpx; background: red; color: white;">
    页面测试 - 当前时间: {{currentTime}}
  </view>

  <!-- 简单轮播图测试 -->
  <view style="height: 300rpx; margin: 20rpx; background: #f0f0f0;">
    <text style="display: block; padding: 20rpx;">轮播图区域</text>
    <swiper style="height: 200rpx;">
      <swiper-item>
        <view style="background: red; color: white; text-align: center; line-height: 200rpx;">
          图片1
        </view>
      </swiper-item>
      <swiper-item>
        <view style="background: blue; color: white; text-align: center; line-height: 200rpx;">
          图片2
        </view>
      </swiper-item>
      <swiper-item>
        <view style="background: green; color: white; text-align: center; line-height: 200rpx;">
          图片3
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 品牌内容区域 -->
  <view class="content-section">
    <!-- 品牌介绍图片 -->
    <view class="brand-intro-section">
      <image
        class="brand-intro-image"
        src="{{brandIntroImage}}"
        mode="aspectFill"
        lazy-load="true"
      />
      <view class="image-placeholder" wx:if="{{!brandIntroImage}}">
        <view class="placeholder-text">品牌介绍图片</view>
        <view class="placeholder-desc">管理员可上传品牌介绍图片</view>
      </view>
    </view>

    <!-- 品牌历程图片 -->
    <view class="brand-history-section">
      <image
        class="brand-history-image"
        src="{{brandHistoryImage}}"
        mode="aspectFill"
        lazy-load="true"
      />
      <view class="image-placeholder" wx:if="{{!brandHistoryImage}}">
        <view class="placeholder-text">品牌历程图片</view>
        <view class="placeholder-desc">管理员可上传品牌历程图片</view>
      </view>
    </view>
  </view>
</view>