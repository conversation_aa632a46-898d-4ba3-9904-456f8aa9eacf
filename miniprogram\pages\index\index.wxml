<!--pages/index/index.wxml-->
<view class="container">
  <!-- 全屏轮播图区域 -->
  <view class="banner-section">
    <swiper
      class="banner-swiper"
      indicator-dots="{{true}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#fff"
      autoplay="{{true}}"
      interval="{{4000}}"
      duration="{{500}}"
      circular="{{true}}"
      current="{{currentBannerIndex}}"
      bindchange="onBannerChange"
      display-multiple-items="{{1}}"
      skip-hidden-item-layout="{{true}}"
    >
      <swiper-item
        wx:for="{{banners}}"
        wx:key="id"
        class="banner-item"
        data-index="{{index}}"
        bindtap="onBannerTap"
      >
        <image
          class="banner-image"
          src="{{item.image}}"
          mode="aspectFill"
          bindload="onBannerImageLoad"
          binderror="onBannerImageError"
          data-index="{{index}}"
        />
        <!-- 轮播图加载状态 -->
        <view class="image-loading" wx:if="{{item.loading}}">
          <view class="loading-spinner"></view>
          <view class="loading-text">加载中...</view>
        </view>
        <!-- 暂时隐藏轮播图文字内容，后续可能恢复使用 -->
        <!-- <view class="banner-overlay">
          <view class="banner-content">
            <view class="banner-title">{{item.title}}</view>
            <view class="banner-subtitle">{{item.subtitle}}</view>
          </view>
        </view> -->
      </swiper-item>
    </swiper>

    <!-- 向下滑动提示 -->
    <view class="scroll-hint">
      <view class="scroll-arrow">↓</view>
    </view>
  </view>

  <!-- 品牌内容区域 -->
  <view class="content-section">
    <!-- 品牌介绍图片 -->
    <view class="brand-intro-section">
      <image
        class="brand-intro-image"
        src="{{brandIntroImage}}"
        mode="aspectFill"
        lazy-load="true"
        bindload="onBrandIntroImageLoad"
        binderror="onBrandIntroImageError"
      />
      <!-- 品牌介绍图片加载状态 -->
      <view class="image-loading" wx:if="{{brandIntroLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
      <view class="image-placeholder" wx:if="{{!brandIntroImage}}">
        <view class="placeholder-text">品牌介绍图片</view>
        <view class="placeholder-desc">管理员可上传品牌介绍图片</view>
      </view>
    </view>

    <!-- 品牌历程图片 -->
    <view class="brand-history-section">
      <image
        class="brand-history-image"
        src="{{brandHistoryImage}}"
        mode="aspectFill"
        lazy-load="true"
        bindload="onBrandHistoryImageLoad"
        binderror="onBrandHistoryImageError"
      />
      <!-- 品牌历程图片加载状态 -->
      <view class="image-loading" wx:if="{{brandHistoryLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>
      <view class="image-placeholder" wx:if="{{!brandHistoryImage}}">
        <view class="placeholder-text">品牌历程图片</view>
        <view class="placeholder-desc">管理员可上传品牌历程图片</view>
      </view>
    </view>


  </view>
</view>