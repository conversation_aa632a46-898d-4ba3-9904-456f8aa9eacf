<!--pages/index/index.wxml-->
<view class="container">


  <!-- 全屏轮播图 -->
  <view style="height: calc(100vh - 98rpx); position: relative;">
    <swiper
      style="height: 100%;"
      indicator-dots="{{true}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#fff"
      autoplay="{{true}}"
      interval="{{4000}}"
      duration="{{500}}"
      circular="{{true}}"
      current="{{currentBannerIndex}}"
      bindchange="onBannerChange"
    >
      <swiper-item
        wx:for="{{banners}}"
        wx:key="id"
        data-index="{{index}}"
        bindtap="onBannerTap"
      >
        <image
          src="{{item.image}}"
          mode="aspectFill"
          style="width: 100%; height: 100%;"
        />
      </swiper-item>
    </swiper>

    <!-- 向下滑动提示 -->
    <view style="position: absolute; bottom: 120rpx; left: 50%; transform: translateX(-50%); text-align: center; color: #fff; animation: gentleBounce 3s infinite; z-index: 10;">
      <view style="font-size: 40rpx; font-weight: bold; opacity: 0.8;">↓</view>
    </view>
  </view>

  <!-- 品牌内容区域 -->
  <view style="background: #fff;">
    <!-- 品牌介绍图片 -->
    <view style="height: 100vh; position: relative;">
      <image
        src="{{brandIntroImage}}"
        mode="aspectFill"
        style="width: 100%; height: 100%;"
        lazy-load="true"
      />
    </view>

    <!-- 品牌历程图片 -->
    <view style="height: 100vh; position: relative;">
      <image
        src="{{brandHistoryImage}}"
        mode="aspectFill"
        style="width: 100%; height: 100%;"
        lazy-load="true"
      />
    </view>
  </view>
</view>