/* pages/index/index.wxss */

/* 页面基础样式 - 首页专用 */
page {
  height: 100vh;
  background: #fff !important; /* 覆盖全局样式，首页使用白色背景 */
  margin: 0;
  padding: 0;
  overflow-x: hidden; /* 防止横向滚动 */
}

.container {
  height: 100vh;
  background: #fff;
  margin: 0;
  padding: 0;
  position: relative;
}

/* 轮播图区域 */
.banner-section {
  position: relative;
  width: 100vw;
  /* 减去底部tabBar高度(约98rpx)，确保轮播图不被遮挡 */
  height: calc(100vh - 98rpx);
  overflow: hidden;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

/* 轮播图指示器样式 */
.banner-swiper .wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: 40rpx;
}

.banner-swiper .wx-swiper-dot {
  width: 60rpx !important;
  height: 6rpx !important;
  border-radius: 3rpx !important;
  background-color: rgba(255, 255, 255, 0.5) !important;
  margin: 0 8rpx !important;
}

.banner-swiper .wx-swiper-dot.wx-swiper-dot-active {
  background-color: #fff !important;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-content {
  text-align: center;
  color: #fff;
  padding: 0 40rpx;
}

.banner-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 滑动提示 */
.scroll-hint {
  position: absolute;
  bottom: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #fff;
  animation: gentleBounce 3s infinite;
  z-index: 10;
}

.scroll-arrow {
  font-size: 40rpx;
  font-weight: bold;
  opacity: 0.8;
}

@keyframes gentleBounce {
  0%, 70%, 100% {
    transform: translateX(-50%) translateY(0);
    opacity: 0.8;
  }
  35% {
    transform: translateX(-50%) translateY(-6rpx);
    opacity: 1;
  }
}

/* 内容区域 */
.content-section {
  background-color: #fff;
  position: relative;
}

/* 品牌介绍图片区域 */
.brand-intro-section {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.brand-intro-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 品牌历程图片区域 */
.brand-history-section {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.brand-history-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 图片占位符样式 */
.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #666;
}

.placeholder-text {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  color: #333;
}

.placeholder-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 图片加载状态样式 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 5;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3cc51f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}





/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
  display: none;
}
